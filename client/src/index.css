@import "tailwindcss";

/* Clean Quill list styling - allow native Quill behavior */
.ql-editor {
  counter-reset: list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}

/* Basic list item styling */
.ql-editor li {
  list-style-type: none;
  padding-left: 1.5rem;
  position: relative;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Bullet list styling */
.ql-editor li[data-list="bullet"] {
  position: relative;
  padding-left: 1.5rem;
}

.ql-editor li[data-list="bullet"]::before {
  content: "•";
  position: absolute;
  left: 0.5rem;
  color: currentColor;
}

/* Ordered list styling with proper counter */
.ql-editor li[data-list="ordered"] {
  position: relative;
  padding-left: 1.5rem;
  counter-increment: list-0;
}

.ql-editor li[data-list="ordered"]::before {
  content: counter(list-0, decimal) ".";
  position: absolute;
  left: 0.25rem;
  color: currentColor;
}

/* Reset counters for new lists */
.ql-editor li[data-list="ordered"]:first-child,
.ql-editor > li[data-list="ordered"]:first-of-type {
  counter-reset: list-0;
}

/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Quill editor specific styling */
.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

/* Basic list container styling */
.ql-editor ul,
.ql-editor ol {
  list-style: none;
  padding-left: 0;
  margin: 0.5rem 0;
}

/* Quill editor general styling */
.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}
