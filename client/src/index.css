@import "tailwindcss";

/* Custom Quill list styling - proper bullet vs ordered list handling */
.ql-editor ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ql-editor ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ql-editor li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Ensure nested lists work properly */
.ql-editor ul ul {
  list-style-type: circle;
  margin: 0.25rem 0;
}

.ql-editor ul ul ul {
  list-style-type: square;
  margin: 0.25rem 0;
}

.ql-editor ol ol {
  list-style-type: lower-alpha;
  margin: 0.25rem 0;
}

.ql-editor ol ol ol {
  list-style-type: lower-roman;
  margin: 0.25rem 0;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}


/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Quill editor specific styling */
.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}
