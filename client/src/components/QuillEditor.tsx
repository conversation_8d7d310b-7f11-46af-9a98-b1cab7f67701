import { useEffect, useRef, useImperativeHandle, forwardRef, useLayoutEffect } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';

interface QuillEditorProps {
  value?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  theme?: 'snow' | 'bubble';
  modules?: Record<string, unknown>;
  formats?: string[];
  className?: string;
}

export interface QuillEditorRef {
  getEditor: () => Quill | null;
  focus: () => void;
  blur: () => void;
}

// Enhanced QuillEditor component based on documentation example
const QuillEditor = forwardRef<QuillEditorRef, QuillEditorProps>(({
  value = '',
  onChange,
  placeholder = '',
  readOnly = false,
  theme = 'snow',
  modules,
  formats,
  className = ''
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<Quill | null>(null);
  const onChangeRef = useRef(onChange);
  const valueRef = useRef(value);

  // Keep refs updated
  useLayoutEffect(() => {
    onChangeRef.current = onChange;
    valueRef.current = value;
  });

  useImperativeHandle(ref, () => ({
    getEditor: () => quillRef.current,
    focus: () => quillRef.current?.focus(),
    blur: () => quillRef.current?.blur()
  }));

  // Initialize Quill
  useEffect(() => {
    if (!containerRef.current || quillRef.current) return;

    const container = containerRef.current;
    const editorContainer = container.appendChild(
      container.ownerDocument.createElement('div')
    );

    const quill = new Quill(editorContainer, {
      theme,
      modules: modules || {
        toolbar: [
          ['bold', 'italic', 'underline'],
          [{ 'list': 'ordered'}, { 'list': 'bullet' }],
          [{ 'header': [1, 2, false] }],
          ['clean']
        ]
      },
      formats: formats || ['bold', 'italic', 'underline', 'header', 'list'],
      placeholder,
      readOnly
    });

    quillRef.current = quill;

    // Set initial content
    if (valueRef.current) {
      quill.root.innerHTML = valueRef.current;
    }

    // Handle text changes
    quill.on(Quill.events.TEXT_CHANGE, () => {
      onChangeRef.current?.(quill.root.innerHTML);
    });

    // Cleanup
    return () => {
      quillRef.current = null;
      container.innerHTML = '';
    };
  }, [theme, placeholder, readOnly, modules, formats]);

  // Update content when value prop changes
  useEffect(() => {
    if (!quillRef.current) return;

    const currentContent = quillRef.current.root.innerHTML;
    if (currentContent !== value) {
      quillRef.current.root.innerHTML = value || '';
    }
  }, [value]);

  // Update readOnly state
  useEffect(() => {
    if (!quillRef.current) return;
    quillRef.current.enable(!readOnly);
  }, [readOnly]);

  return (
    <div className={`quill-wrapper ${className}`}>
      <div ref={containerRef} />
    </div>
  );
});

QuillEditor.displayName = 'QuillEditor';

export default QuillEditor;
